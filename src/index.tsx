import Anthropic from "@anthropic-ai/sdk";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { sign, verify } from "hono/jwt";
import { renderer } from "./renderer";
import { prompt } from "./utils/prompt";

interface CleanCodeRequest {
  code: string;
  imageUrl: string;
}

interface CleanCodeResponse {
  cleanCode: string;
  suggestions?: string[];
}

// No request body needed for token generation

type Bindings = {
  ANTHROPIC_API_KEY: string;
  JWT_SECRET: string;
};

type Variables = {
  requestId: string;
  startTime: number;
};

const app = new Hono<{ Bindings: Bindings; Variables: Variables }>();

// Request logging middleware
app.use("*", async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const url = c.req.url;
  const userAgent = c.req.header("User-Agent") || "Unknown";
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log(
    `[${new Date().toISOString()}] [${requestId}] --> ${method} ${url}`
  );
  console.log(
    `[${new Date().toISOString()}] [${requestId}] User-Agent: ${userAgent}`
  );

  // Log request headers (excluding sensitive ones)
  const headers = Object.fromEntries(
    Object.entries(c.req.header()).filter(
      ([key]) =>
        !key.toLowerCase().includes("authorization") &&
        !key.toLowerCase().includes("cookie")
    )
  );
  console.log(
    `[${new Date().toISOString()}] [${requestId}] Headers:`,
    JSON.stringify(headers, null, 2)
  );

  // Store request ID and start time for response logging
  c.set("requestId", requestId);
  c.set("startTime", start);

  await next();

  const end = Date.now();
  const duration = end - start;
  const status = c.res.status;

  console.log(
    `[${new Date().toISOString()}] [${requestId}] <-- ${status} ${method} ${url} (${duration}ms)`
  );
});

app.use(renderer);

app.use(
  "/api/*",
  cors({
    origin: ["https://figma.com"],
    allowMethods: ["GET", "POST", "OPTIONS"],
  })
);

// Generate token endpoint
app.get("/api/auth/token", async (c) => {
  const requestId = c.get("requestId");
  console.log(
    `[${new Date().toISOString()}] [${requestId}] Generating JWT token`
  );

  const token = await sign(
    { exp: Math.floor(Date.now() / 1000) + 15 * 60 },
    c.env.JWT_SECRET
  );

  console.log(
    `[${new Date().toISOString()}] [${requestId}] JWT token generated successfully`
  );
  return c.json({ token });
});

// Modify auth middleware to use JWT
app.use("/api/clean-code/*", async (c, next) => {
  const requestId = c.get("requestId");
  const authHeader = c.req.header("Authorization");

  console.log(
    `[${new Date().toISOString()}] [${requestId}] Authenticating request`
  );

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Authentication failed: Missing or invalid Authorization header`
    );
    return c.json({ error: "Unauthorized" }, 401);
  }

  const token = authHeader.split(" ")[1];
  try {
    await verify(token, c.env.JWT_SECRET);
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Authentication successful`
    );
    await next();
  } catch (e) {
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Authentication failed: Invalid token - ${
        e instanceof Error ? e.message : "Unknown error"
      }`
    );
    return c.json({ error: "Invalid token" }, 401);
  }
});

app.post("/api/clean-code", async (c) => {
  const requestId = c.get("requestId");
  const { code, imageUrl } = await c.req.json<CleanCodeRequest>();
  const apiKey = c.env.ANTHROPIC_API_KEY;

  console.log(
    `[${new Date().toISOString()}] [${requestId}] Processing clean-code request`
  );
  console.log(
    `[${new Date().toISOString()}] [${requestId}] Code length: ${
      code.length
    } characters`
  );
  console.log(
    `[${new Date().toISOString()}] [${requestId}] Image URL provided: ${!!imageUrl}`
  );

  if (!apiKey) {
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Error: Anthropic API key not configured`
    );
    const res: CleanCodeResponse = {
      cleanCode: "No Anthropic API Key",
      suggestions: ["Error: Anthropic API key not configured on the server."],
    };
    return c.json(res, 500);
  }

  try {
    const client = new Anthropic({ apiKey });
    const base64Image = imageUrl.split(",")[1] || "";

    console.log(
      `[${new Date().toISOString()}] [${requestId}] Calling Anthropic API`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Model: claude-3-5-sonnet-latest`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Max tokens: 4000`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Image data length: ${
        base64Image.length
      } characters`
    );

    const anthropicStart = Date.now();
    const res = await client.messages.create({
      model: "claude-3-5-sonnet-latest",
      max_tokens: 4000,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `${prompt}\nClean and optimize this code:\n${code}`,
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/png",
                data: base64Image,
              },
            },
          ],
        },
      ],
    });
    const anthropicDuration = Date.now() - anthropicStart;

    console.log(
      `[${new Date().toISOString()}] [${requestId}] Anthropic API response received (${anthropicDuration}ms)`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Response ID: ${res.id}`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Usage - Input tokens: ${
        res.usage.input_tokens
      }, Output tokens: ${res.usage.output_tokens}`
    );
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Content blocks: ${
        res.content.length
      }`
    );

    const textPart = res.content.find((c: any) => c.type === "text") as any;
    const cleanCode = textPart?.text || "";

    console.log(
      `[${new Date().toISOString()}] [${requestId}] Extracted clean code length: ${
        cleanCode.length
      } characters`
    );

    const response: CleanCodeResponse = {
      cleanCode,
      suggestions: [],
    };

    console.log(
      `[${new Date().toISOString()}] [${requestId}] Clean-code request completed successfully`
    );
    return c.json(response, 200);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.log(
      `[${new Date().toISOString()}] [${requestId}] Error processing clean-code request: ${errorMessage}`
    );

    if (error instanceof Error) {
      console.log(
        `[${new Date().toISOString()}] [${requestId}] Error stack: ${
          error.stack
        }`
      );
    }

    const resp: CleanCodeResponse = {
      cleanCode: code,
      suggestions: [errorMessage],
    };
    return c.json(resp, 500);
  }
});

app.get("/", (c) => {
  const requestId = c.get("requestId");
  console.log(`[${new Date().toISOString()}] [${requestId}] Serving home page`);
  return c.render(<h1>Hello!</h1>);
});

export default app;
